"use client"

import { useState, use<PERSON><PERSON>back, useEffect } from "react"
import { useParams } from "next/navigation"
import { useUser } from "../auth/auth.hooks"
import { useIsUserSubscribed } from "../user-subscription/user-subscription.hooks"
import { useRealtimeUserAIUsage } from "../user-ai-usage/user-ai-usage.realtime.hooks"
import { UserAIUsageService } from "../user-ai-usage/user-ai-usage.service"
import { AIUsageCategory } from "../user-ai-usage/user-ai-usage.types"
import { useAISuggestionsCache } from "./ai-suggestions-cache.hooks"
import { CachedTripSuggestion } from "./ai-suggestions-cache.service"
import { SubscriptionErrorType } from "../user-subscription/user-subscription.types"
import { generateTripSuggestions } from "@/lib/api-client"
import { useUserPreferences } from "../user-preferences/user-preferences.hooks"
import { isValidTravelType } from "@/lib/constants/travel-types"
import {
  getLocationImage,
  getLocationImageByPlaceId,
  searchPlaces,
  fetchGooglePlaceImage,
  fetchGooglePlaceImageByPlaceId,
} from "@/lib/google-places"
import { toast } from "@/components/ui/use-toast"

export type LocationPreference = "local" | "national" | "global"

/**
 * Extended trip suggestions hook return type with location preferences
 */
export interface TripSuggestionsWithLocationHookReturn {
  /** The suggestions returned from the AI */
  suggestions: CachedTripSuggestion[]
  /** Whether a request is in progress */
  loading: boolean
  /** Error object for usage limit errors */
  usageError: {
    show: boolean
    errorType: SubscriptionErrorType
    usageData: {
      daily: number
      weekly: number
      dailyLimit: number
      weeklyLimit: number
      categoryCount?: number
      categoryLimit?: number
    }
  } | null
  /** Generic error object for API failures */
  error: string | null
  /** Whether cached suggestions are being used */
  usingCachedSuggestions: boolean
  /** Whether suggestions have been loaded */
  suggestionsLoaded: boolean
  /** Current location preference */
  locationPreference: LocationPreference
  /** Whether user has location configured */
  hasUserLocation: boolean
  /** Function to load suggestions with location preference */
  loadSuggestions: (refresh?: boolean, locationPref?: LocationPreference) => Promise<void>
  /** Function to set location preference */
  setLocationPreference: (preference: LocationPreference) => void
  /** Function to check if the user can make a request */
  canMakeRequest: (category: AIUsageCategory) => Promise<boolean>
}

/**
 * Hook for AI trip suggestions with location preferences
 * @param squadId Optional squad ID (if not provided, will use the ID from the URL params)
 * @returns Trip suggestions hook return object with location support
 */
export function useAITripSuggestionsWithLocation(
  squadId?: string
): TripSuggestionsWithLocationHookReturn {
  const isSubscribed = useIsUserSubscribed()
  const params = useParams()
  const id = squadId || (params.id as string)
  const currentUser = useUser()
  const { preferences } = useUserPreferences(currentUser?.uid || "")
  const { usage, getCategoryUsage } = useRealtimeUserAIUsage(isSubscribed)
  const { hasCachedSuggestions, getTripSuggestions, saveTripSuggestions, clearCachedSuggestions } =
    useAISuggestionsCache()

  // State for suggestions
  const [suggestions, setSuggestions] = useState<CachedTripSuggestion[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [suggestionsLoaded, setSuggestionsLoaded] = useState(false)
  const [usingCachedSuggestions, setUsingCachedSuggestions] = useState(false)
  const [usageError, setUsageError] =
    useState<TripSuggestionsWithLocationHookReturn["usageError"]>(null)
  const [locationPreference, setLocationPreferenceState] = useState<LocationPreference>("global")

  // Check if user has location configured
  const hasUserLocation = Boolean(preferences?.location && preferences?.locationPlaceId)

  // Load cached suggestions for current preference on mount or when preference changes
  useEffect(() => {
    if (currentUser?.uid) {
      const cacheKey = `${id}-${locationPreference}`
      const cachedSuggestions = getTripSuggestions(cacheKey, currentUser.uid)

      if (cachedSuggestions && cachedSuggestions.length > 0) {
        setSuggestions(cachedSuggestions)
        setSuggestionsLoaded(true)
        setUsingCachedSuggestions(true)
      } else {
        // No cached suggestions for this preference
        setSuggestions([])
        setSuggestionsLoaded(false)
        setUsingCachedSuggestions(false)
      }
    }
  }, [currentUser?.uid, id, locationPreference, getTripSuggestions])

  /**
   * Check if the user can make an AI request
   * @param category AI usage category
   * @returns Promise<boolean> Whether the user can make a request
   */
  const canMakeRequest = useCallback(
    async (category: AIUsageCategory): Promise<boolean> => {
      if (!currentUser?.uid) return false

      try {
        // Default to false (free user) if subscription status is undefined
        const subscriptionStatus = isSubscribed ?? false

        // For pro users, they can always make requests
        if (subscriptionStatus) {
          setUsageError(null)
          return true
        }

        // For free users, check category-specific limits using real-time data
        const categoryUsage = getCategoryUsage(category)
        if (!categoryUsage) {
          // If no category data exists yet, allow the request
          setUsageError(null)
          return true
        }

        // Check if they've reached the category limit
        if (categoryUsage.count >= categoryUsage.limit) {
          setUsageError({
            show: true,
            errorType: SubscriptionErrorType.TRIP_AI_LIMIT_REACHED,
            usageData: {
              daily: usage?.daily || 0,
              weekly: usage?.weekly || 0,
              dailyLimit: usage?.dailyLimit || 10,
              weeklyLimit: usage?.weeklyLimit || 50,
              categoryCount: categoryUsage.count,
              categoryLimit: categoryUsage.limit,
            },
          })
          return false
        }

        setUsageError(null)
        return true
      } catch (error) {
        console.error("Error checking AI request permission:", error)
        return false
      }
    },
    [currentUser?.uid, isSubscribed, getCategoryUsage, usage]
  )

  /**
   * Set location preference (cached suggestions will be loaded by useEffect)
   */
  const setLocationPreference = useCallback((preference: LocationPreference) => {
    setLocationPreferenceState(preference)
    // The useEffect above will handle loading cached suggestions for the new preference
  }, [])

  /**
   * Load trip suggestions with location preference
   * @param forceRefresh Whether to force a refresh (bypass cache)
   * @param locationPref Optional location preference override
   */
  const loadSuggestions = useCallback(
    async (forceRefresh: boolean = false, locationPref?: LocationPreference): Promise<void> => {
      if (!currentUser?.uid) {
        toast({
          title: "Error",
          description: "You must be logged in to generate trip suggestions.",
          variant: "destructive",
        })
        return
      }

      const currentLocationPref = locationPref || locationPreference

      // Check if user has location for local/national preferences
      if (
        (currentLocationPref === "local" || currentLocationPref === "national") &&
        !hasUserLocation
      ) {
        setError("Please configure your location in settings to use local or national suggestions.")
        return
      }

      try {
        setLoading(true)
        setError(null)

        // Create cache key that includes location preference
        const cacheKey = `${id}-${currentLocationPref}`

        // If we're forcing a refresh, always make a new request
        if (forceRefresh) {
          setUsingCachedSuggestions(false)
        }
        // Otherwise, check for cached suggestions with location preference
        else if (hasCachedSuggestions(AIUsageCategory.TRIP, cacheKey, currentUser.uid)) {
          // Get cached suggestions
          const cachedSuggestions = getTripSuggestions(cacheKey, currentUser.uid)

          if (cachedSuggestions && cachedSuggestions.length > 0) {
            // Use cached suggestions
            setSuggestions(cachedSuggestions)
            setSuggestionsLoaded(true)
            setUsingCachedSuggestions(true)
            setLoading(false)
            return
          }
        }

        // Check if the user can make a request
        const canMakeAIRequest = await canMakeRequest(AIUsageCategory.TRIP)
        if (!canMakeAIRequest) {
          setLoading(false)
          return
        }

        // Prepare user preferences for the API
        const userPreferences = {
          travelPreferences: preferences?.travelPreferences || [],
          budgetRange: preferences?.budgetRange || [500, 2000],
          preferredTravelSeasons: preferences?.preferredTravelSeasons || [],
          availabilityPreferences: preferences?.availabilityPreferences || [],
          travelGroupPreferences:
            preferences?.travelGroupPreferences && preferences.travelGroupPreferences.length > 0
              ? preferences.travelGroupPreferences
              : [],
        }

        // Prepare user location data
        const userLocation = hasUserLocation
          ? {
              location: preferences!.location,
              locationPlaceId: preferences!.locationPlaceId,
            }
          : undefined

        // Call the API to generate trip suggestions with location preference
        const result = await generateTripSuggestions(
          userPreferences,
          forceRefresh ? undefined : suggestions.length > 0 ? suggestions : undefined,
          currentLocationPref,
          userLocation
        )

        if (!result || !Array.isArray(result)) {
          throw new Error("Failed to generate trip suggestions")
        }

        // Process suggestions with images (same as original hook)
        const processedSuggestions = await Promise.all(
          result.map(async (suggestion) => {
            try {
              let imageUrl = "/placeholder.svg?height=150&width=300"
              let googlePlaceImage = null
              let attribution = undefined

              // Try to get Google Places image
              try {
                const googleImageResult = await fetchGooglePlaceImageByPlaceId(
                  suggestion.placeId || "",
                  suggestion.destination
                )
                if (googleImageResult) {
                  imageUrl = googleImageResult.imageUrl
                  googlePlaceImage = {
                    imageUrl: googleImageResult.imageUrl,
                    sourceId: googleImageResult.sourceId,
                    attribution: googleImageResult.attribution,
                  }
                  attribution = googleImageResult.attribution
                }
              } catch (error) {
                console.error("Error fetching Google Places image:", error)
              }

              return {
                ...suggestion,
                // Keep legacy image field for backward compatibility
                image: imageUrl,
                // Add new GooglePlaceImage field
                googlePlaceImage,
                attribution,
              }
            } catch (error) {
              console.error(`Error processing suggestion for ${suggestion.destination}:`, error)
              // Return suggestion with placeholder image
              return {
                ...suggestion,
                image: "/placeholder.svg?height=150&width=300",
              }
            }
          })
        )

        // Filter suggestions to ensure they only use valid travel types
        const validatedSuggestions = processedSuggestions.map((suggestion) => ({
          ...suggestion,
          tags: suggestion.tags.filter((tag) => isValidTravelType(tag)),
        }))

        // Cache the suggestions with location preference in key
        if (currentUser?.uid) {
          saveTripSuggestions(cacheKey, currentUser.uid, validatedSuggestions)
        }

        // Only increment the user's AI usage counter if we successfully generated and processed suggestions
        // and we're not using cached suggestions
        if (!usingCachedSuggestions) {
          await UserAIUsageService.incrementAIUsage(currentUser.uid, AIUsageCategory.TRIP)
        }

        setSuggestions(validatedSuggestions)
        setSuggestionsLoaded(true)
      } catch (err) {
        console.error("Error fetching trip suggestions:", err)
        setError("Failed to load trip suggestions. Please try again.")
        toast({
          title: "Error",
          description: "Failed to load trip suggestions. Please try again.",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    },
    [
      currentUser,
      id,
      preferences,
      locationPreference,
      hasUserLocation,
      hasCachedSuggestions,
      getTripSuggestions,
      saveTripSuggestions,
      canMakeRequest,
      suggestions,
      usingCachedSuggestions,
    ]
  )

  return {
    suggestions,
    loading,
    error,
    usageError,
    suggestionsLoaded,
    usingCachedSuggestions,
    locationPreference,
    hasUserLocation,
    loadSuggestions,
    setLocationPreference,
    canMakeRequest,
  }
}
